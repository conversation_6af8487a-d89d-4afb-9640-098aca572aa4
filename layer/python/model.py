from concurrent.futures import ThreadPoolExecutor
import time
from time import localtime
from hashlib import md5
from langchain_core.documents import Document
from PyPDF2 import PdfReader, PdfWriter
from dotenv import load_dotenv
from google.genai import types
from google import genai
from typing import List
from tqdm import tqdm
import json
import os
import sys
from google.oauth2 import service_account
import time

load_dotenv()

API_KEY = "[your-api-key]"
PROJECT_ID = "gen-lang-client-**********"

if not API_KEY or API_KEY == "[your-api-key]":
    API_KEY = os.getenv('GEMINI_API_KEY')

client = genai.Client(api_key=API_KEY)
# client 2 for batch processing
credentials_path=os.getenv("SERVICE_ACCOUNT_CREDENTIALS")

# Method 1: Using service account credentials file
# credentials_path = '/Users/<USER>/Documents/Startup/aegis-grader/gen-lang-client-**********-0c98c9c1ef92.json'
# credentials = service_account.Credentials.from_service_account_file(
#     credentials_path,
#     scopes=['https://www.googleapis.com/auth/cloud-platform']
# )
# credentials = service_account.Credentials.service_account_email
# Initialize the client
# client2 = genai.Client(
#     project=PROJECT_ID,
#     location="asia-south1",
#     # credentials=credentials,
#     http_options=types.HttpOptions(api_version='v1')
# )

def generate(b64Page, prompt, metadata, max_retries=3, retry_delay=2):
    for attempt in range(max_retries):
        try:
            document = types.Part.from_bytes(
                data=b64Page,
                mime_type="application/pdf",
            )

            model = "gemini-2.5-flash-lite"
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        document,
                        types.Part.from_text(text=prompt)
                    ]
                ),
            ]
            
            generate_content_config = types.GenerateContentConfig(
                temperature=0,
                top_p=0.95,
                max_output_tokens=65536,
                response_modalities=["TEXT"],
                safety_settings=[
                    types.SafetySetting(category="HARM_CATEGORY_HATE_SPEECH", threshold="OFF"),
                    types.SafetySetting(category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="OFF"),
                    types.SafetySetting(category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="OFF"),
                    types.SafetySetting(category="HARM_CATEGORY_HARASSMENT", threshold="OFF")
                ], 
                response_mime_type="text/plain",
                # system_instruction=[types.Part.from_text(text=prompt)],
                # thinking_config=types.ThinkingConfig(thinking_budget=512)  # Enable thinking mode
            )

            response = client.models.generate_content(
                model=model,
                contents=contents,
                config=generate_content_config,
            )
            
            page_content = response.text
            return Document(page_content=page_content, metadata=metadata)
            
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {str(e)}", file=sys.stderr)
            if attempt < max_retries - 1:
                print(f"Retrying in {retry_delay} seconds...", file=sys.stderr)
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print(f"All {max_retries} attempts failed for page generation", file=sys.stderr)
                raise


def batchGenerateRequest(b64Page, prompt, page_number):
    """
    Creates a batch request for generating content from multiple PDF pages.
    """
    formatted_prompt = prompt.replace("{{page_number}}", page_number)
    contents = []
    document = types.Part.from_bytes(
        data=b64Page,
        mime_type="application/pdf",
    )
        
    contents.append(
        types.Content(
            role="user",
            parts=[
                document,
                types.Part.from_text(text=formatted_prompt)
            ]
        )
    )
    
    return {"contents": contents}

def batchEvaluateRequest(answer_sheet_markdown, rubric_markdown, evaluation_prompt):
    formatted_prompt = evaluation_prompt.replace(
        "{rubric_json}", rubric_markdown).replace(
        "{answer_sheet_json}", answer_sheet_markdown
    )
    
    contents = [
        types.Content(
            role="user",
            parts=[types.Part.from_text(text=formatted_prompt)]
        ),
    ]

    return {"contents" : contents}
    

def evaluate(answer_sheet_markdown, rubric_markdown, evaluation_prompt, format=None, rubricOnly=None):
    # Instead of using string formatting which can conflict with LaTeX expressions,
    # we'll replace the placeholders manually
    formatted_prompt = None
    if (rubricOnly):
        # For rubric generation prompts, replace {raw_document_text} instead of {rubric_json}
        if "{raw_document_text}" in evaluation_prompt:
            formatted_prompt = evaluation_prompt.replace("{raw_document_text}", rubric_markdown)
        else:
            formatted_prompt = evaluation_prompt.replace("{rubric_json}", rubric_markdown)
    else:
        formatted_prompt = evaluation_prompt.replace(
            "{rubric_json}", rubric_markdown).replace(
            "{answer_sheet_json}", answer_sheet_markdown
        )
    
    model = "gemini-2.5-flash-lite"
    # model = "gemini-2.5-flash"
    contents = [
        types.Content(
            role="user",
            parts=[types.Part.from_text(text=formatted_prompt)]
        ),
    ]
    
    generate_content_config = types.GenerateContentConfig(
        temperature=0,
        top_p=0.95,
        max_output_tokens=65536,
        response_modalities=["TEXT"],
        safety_settings=[
            types.SafetySetting(category="HARM_CATEGORY_HATE_SPEECH", threshold="OFF"),
            types.SafetySetting(category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="OFF"),
            types.SafetySetting(category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="OFF"),
            types.SafetySetting(category="HARM_CATEGORY_HARASSMENT", threshold="OFF")
        ],
        # Since the response is now in Markdown format with custom tags, not JSON
        response_mime_type="text/plain" if not format else format,
        # system_instruction=[types.Part.from_text(text=formatted_prompt)],
        # thinking_config=types.ThinkingConfig(thinking_budget=6144)  # Enable thinking mode

    )
    
    try:
        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=generate_content_config,
        )
    except Exception as _:
        print("retrying in 60s...")
        time.sleep(60) # Retry after a 1min delay
        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=generate_content_config,
        )

    
    # The response is now plain text (Markdown with custom tags)
    evaluation_result = response.text
    
    # Create a Document object with evaluation results
    metadata = {
        "type": "evaluation",
        "answer_sheet": "processed answer sheet",
        "rubric": "processed rubric"
    }
    
    return Document(
        page_content=evaluation_result,
        metadata=metadata
    )


def batchProcessing(inlineRequestList, prompt, model="models/gemini-2.5-flash", write_result=True):
    print(f"[Mehul] got api key: {API_KEY}")
    all_evaluation_contents = []
    created_md_files = []

    # batch_config = types.CreateBatchJobConfig(
    #     temperature=0,
    #     top_p=0.95,
    #     max_output_tokens=65536,
    #     response_modalities=["TEXT"],
    #     safety_settings=[
    #         types.SafetySetting(category="HARM_CATEGORY_HATE_SPEECH", threshold="OFF"),
    #         types.SafetySetting(category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="OFF"),
    #         types.SafetySetting(category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="OFF"),
    #         types.SafetySetting(category="HARM_CATEGORY_HARASSMENT", threshold="OFF")
    #     ],
    #     # Since the response is now in Markdown format with custom tags, not JSON
    #     response_mime_type="text/plain",
    #     system_instruction=[types.Part.from_text(text=prompt)],
    # )

    try:
        batch_job = client2.batches.create(
            model=model,
            src=inlineRequestList,
            config={
                'display_name': f"Batch Job - {localtime()}",
            }
        )
    except Exception as e:
        time.sleep(10)
        print(f"Error creating batch job: {e}")
        batch_job = client2.batches.create(
            model=model,
            src=inlineRequestList,
            config={
                'display_name': f"Batch Job - {localtime()}",
            }
        )


    job_name = batch_job.name
    print(f"[Mehul] created batch job: {job_name}", file=sys.stderr)
    completed_states = set([
        'JOB_STATE_SUCCEEDED',
        'JOB_STATE_FAILED',
        'JOB_STATE_CANCELLED',
    ])
    # progress_bar = tqdm(total=len(inlineRequestList), desc="Batch Job Progress", unit="requests")
    # currentProcessed = 0
    # lastProcessed = 0
    while batch_job.state.name not in completed_states:
        # print(f"Current state: {batch_job.state.name}", file=sys.stderr)
        time.sleep(30) # Wait for 30 seconds before polling again
        batch_job = client2.batches.get(name=job_name)
    #     currentProcessed = 
    #     if (currentProcessed > lastProcessed):
    #         progress_bar.update(currentProcessed - lastProcessed)
    #         lastProcessed = currentProcessed
    # progress_bar.close()


    print(f"Job finished with state: {batch_job.state.name}", file=sys.stderr)
    if batch_job.state.name == 'JOB_STATE_FAILED':
        print(f"Error: {batch_job.error}", file=sys.stderr)
    elif batch_job.state.name == 'JOB_STATE_SUCCEEDED':
        for i, inline_response in enumerate(batch_job.dest.inlined_responses):
            print(f"Response {i+1}:", file=sys.stderr)
            if inline_response.response:
                # Accessing response, structure may vary.
                try:
                    # print(inline_response.response.text)
                    evaluation_content = inline_response.response.text
                    all_evaluation_contents.append(evaluation_content)
                    result_filename = f"/tmp/evaluation_result_sheet_{i+1}.md"
                    created_md_files.append(result_filename)
                    try:
                        if (evaluation_content and write_result):
                            with open(result_filename, "w") as md_file:
                                md_file.write(evaluation_content)
                        print(f"Detailed evaluation for sheet {i+1} saved to {result_filename}", file=sys.stderr)
                    except IOError as e:
                        print(f"Warning: Could not save detailed evaluation file {result_filename}: {e}", file=sys.stderr)
                    print(f"--- Finished Answer Sheet {i+1} ---", file=sys.stderr)
                except AttributeError:
                    print(inline_response.response) # Fallback
            elif inline_response.error:
                print(f"Error: {inline_response.error}", file=sys.stderr)
    else:
        print(f"Error: Unexpected job state: {batch_job.state.name}", file=sys.stderr)


    return (all_evaluation_contents, created_md_files)
