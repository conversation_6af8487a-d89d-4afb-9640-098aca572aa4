# Standard library imports
import json
import sys
import time
import traceback
from contextlib import ExitStack
from tempfile import NamedTemporaryFile

# Local imports
from processOCR import process_all_pages, combine_and_evaluate, create_rubric, extract_content_from_docs, clean_json_response
from mongoOps import get_mongo_client
from model import evaluate
from newPrompts import rubric_generation_and_validator_prompts


subject_to_prompt_mapping = {
    "Paper A: Compulsory Indian Language": "paper_a_indian_language",
    "Paper B: Compulsory English": "paper_b_english",
    "General Studies Paper I": "gs_1",
    "General Studies Paper II": "gs_2",
    "General Studies Paper III": "gs_3",
    "General Studies Paper IV": "gs_4",
    "Essay": "essay",
    "Optional Subject": "optional_subject"
}

PDF_BASE64_PREFIX = "data:application/pdf;base64,"



# Utility to save PDF bytes to a temp file
def save_pdf_bytes_to_tempfile(pdf_bytes, suffix):
    temp_pdf_file = NamedTemporaryFile(mode="wb", suffix=f"_{suffix}.pdf", delete=False)
    temp_pdf_file.write(pdf_bytes)
    temp_pdf_file.flush()
    return temp_pdf_file

def doRubric(rubricPdfBytes, questionPaperPdfBytes=None, subject=None, jobId=None, save_md_files=False):
    """
    Process the rubric PDF bytes and return rubric content directly.
    Optimized to return content instead of file paths.
    """
    client = get_mongo_client()
    if not rubricPdfBytes:
        if (not questionPaperPdfBytes):
            raise ValueError("No rubric PDF bytes provided.")
        print("No rubric provided. Generating rubric from question paper...", file=sys.stderr)
        temp_qp_pdf = save_pdf_bytes_to_tempfile(questionPaperPdfBytes, "question_paper")
        # Process question paper to get document objects directly
        question_paper_docs = process_all_pages(temp_qp_pdf.name, "question_paper", subject, jobId,client)
        # Generate rubric content directly from documents
        rubric_content = create_rubric(question_paper_docs, save_md_files)
        rubric_result = evaluate(None, rubric_content, rubric_generation_and_validator_prompts[subject_to_prompt_mapping[subject]], format="application/json", rubricOnly=True)
        filterRubric = clean_json_response(rubric_result.page_content)

        return filterRubric
    temp_rubric_pdf = save_pdf_bytes_to_tempfile(rubricPdfBytes, "rubric")
    # Process rubric to get document objects directly
    rubric_docs = process_all_pages(temp_rubric_pdf.name, "rubric", subject, jobId, client)
    # Extract content directly from documents
    rubric_content = extract_content_from_docs(rubric_docs)

    rubricFilterPrompt = rubric_generation_and_validator_prompts[subject_to_prompt_mapping[subject]]

    rubric_result = evaluate(None, rubric_content, rubricFilterPrompt, format="application/json", rubricOnly=True)
    filterRubric = clean_json_response(rubric_result.page_content)
    client.close()
    print(f"[Mehul] got filterrubric: {filterRubric}", file=sys.stderr)
    return filterRubric

def doParallelAnswerSheet(answerSheetBytesKeyTuple, rubricContentAndSubject, child_conn):
    """
    Process the answer sheet PDF bytes against the rubric content.
    Optimized to work with direct content instead of file paths.
    Returns evaluation content as a string.
    """
    answerSheetKey = ""
    try:
        rubric_content = rubricContentAndSubject[0]
        subject = rubricContentAndSubject[1]
        answerSheetBytes = answerSheetBytesKeyTuple[0]
        answerSheetKey = answerSheetBytesKeyTuple[1]

        if not answerSheetBytes:
            raise ValueError("No answer sheet PDF bytes provided.")

        temp_answer_sheet_pdf = save_pdf_bytes_to_tempfile(answerSheetBytes, "answers")
        client = get_mongo_client()
        # Process answer sheet to get document objects directly
        answer_sheet_docs = process_all_pages(temp_answer_sheet_pdf.name, "answer_sheet", subject, answerSheetKey,client)
        answerSheetData = extract_content_from_docs(answer_sheet_docs)

        print(f"Evaluating answer sheet against rubric...", file=sys.stderr)
        # Pass document objects and content directly
        filterAnswerSheet, evaluation_result_doc = combine_and_evaluate(answer_sheet_docs, rubric_content, subject, answerSheetKey,client)
        client.close()
        # if not hasattr(evaluation_result_doc, 'page_content'):
        #     raise ValueError("Evaluation result lacks 'page_content'. Cannot proceed.")

        child_conn.send((evaluation_result_doc, answerSheetKey, answerSheetData, rubric_content, filterAnswerSheet)) # Return the evaluation content as a string
    except Exception as e:
        print(f"Error processing answer sheet, e: {e}")
        # Send 5 values to match the expected format, with error message as first value
        child_conn.send(("Error: processing answer sheet, please try again", answerSheetKey, "", "", ""))

# Keep the old CLI entry point for local testing if needed
if __name__ == "__main__":
    print("This script is now intended to be used as a module for Lambda integration.", file=sys.stderr)
