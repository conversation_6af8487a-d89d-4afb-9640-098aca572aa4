# Standard library imports
import json
import os
import sys
from datetime import datetime

# Third-party imports
import pymongo
import requests
from bson import ObjectId

# MongoDB setup
def get_mongo_client():
    # Use test DB if running locally, prod DB otherwise
    is_local = os.environ.get('AWS_SAM_LOCAL')
    env_var = 'MONGO_URI_TEST' if is_local else 'MONGO_URI_PROD'
    mongo_uri = os.environ.get(env_var)
    
    print(f"Environment: {'LOCAL' if is_local else 'PROD'}")
    print(f"Using environment variable: {env_var}")
    print(f"Connection string exists: {bool(mongo_uri)}")
    
    if not mongo_uri:
        raise ValueError(f"{env_var} environment variable not set")
    
    # Log connection string (masked for security)
    masked_uri = mongo_uri.replace(mongo_uri.split('@')[0].split('//')[1], '***:***') if '@' in mongo_uri else mongo_uri
    print(f"Connecting to: {masked_uri}")
    
    try:
        client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        # Test the connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        return client
    except Exception as e:
        print(f"❌ MongoDB connection failed: {str(e)}")
        raise

def save_progress(client, progress, jobid, status="processing"):
    """Save progress to MongoDB"""
    try:
        dbName = 'AegisScholarTestDb' if os.environ.get('AWS_SAM_LOCAL') else 'AegisScholarDb'
        db = client[dbName]
        collection = db['PollTime']
        
        progress_doc = {
            "jobId": jobid,
            "currentProgress": progress*100,
            "status": status,
            "timestamp": datetime.now()
        }

        if (collection.find_one({"jobId": jobid})):
            # Update existing progress
            collection.update_one({"jobId": jobid}, {"$set": progress_doc})
            print(f"Progress updated for job ID: {jobid}")
            return
        
        result = collection.insert_one(progress_doc)
        print(f"Progress saved with ID: {result.inserted_id}")
        
    except Exception as e:
        print(f"Error saving progress to MongoDB: {str(e)}")

def create_grading_document(manifest_data, client):
    """Create the initial grading document with initialized answer sheets array"""
    try:
        print("Creating initial grading document...")

        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'
        print(f"Using database: {db_name}")

        db = client[db_name]
        collection = db['AegisGrader']

        # Extract files by purpose
        question_paper = None
        rubric = None

        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'question_paper':
                question_paper = file
            elif file.get('filePurpose') == 'rubric':
                rubric = file

        # Get total answer sheets count for processing stats
        total_answer_sheets = len([f for f in manifest_data.get('files', []) if f.get('filePurpose') == 'answer_sheet'])

        initialized_answer_sheets = []
        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'answer_sheet':
                initialized_answer_sheets.append({
                    "id": file.get('key'),
                    "studentName": file.get('studentName', ''),
                    "rollNumber": file.get('rollNumber', ''),
                    "pdfUrl": file.get('key'),  # Using S3 key as URL reference
                    "timestamp": file.get('timestamp'),
                    "evaluationResult": "",
                    "ocr" : "",
                    "filter" : "",
                    "status": "pending",
                    "processedAt": None
                })

        # Create the main document with initialized answer sheets array
        aegis_grader_doc = {
            "testDetails": {
                "createdBy": manifest_data['testDetails'].get('CreatedBy', ''),
                "className": manifest_data['testDetails'].get('className', ''),
                "subject": manifest_data['testDetails'].get('subject', ''),
                "date": manifest_data['testDetails'].get('date', datetime.now().isoformat())
            },
            "answerSheets": initialized_answer_sheets,
            "questionPaper": {
                "type": "questionPaper",
                "pdfUrl": question_paper.get('key', '') if question_paper else '',
                "timestamp": question_paper.get('timestamp') if question_paper else int(datetime.now().timestamp() * 1000)
            },
            "rubric": {
                "type": "rubric",
                "pdfUrl": rubric.get('key', '') if rubric else '',
                # "ocrData" : "",
                "timestamp": rubric.get('timestamp') if rubric else int(datetime.now().timestamp() * 1000)
            },
            "processingStats": {
                "totalAnswerSheets": total_answer_sheets,
                "successfulEvaluations": 0,
                "failedEvaluations": 0,
                "completedAt": None,
                "processingStartedAt": datetime.now(),
                "overallStatus": "processing"
            },
            "creditInfo": {
                "totalCreditsCharged": manifest_data.get('creditInfo', {}).get('totalCreditsCharged', 0),
                "creditsRefunded": manifest_data.get('creditInfo', {}).get('creditsRefunded', 0),
                "originalTransactionId": manifest_data.get('creditInfo', {}).get('originalTransactionId', ''),
                "refundTransactionIds": manifest_data.get('creditInfo', {}).get('refundTransactionIds', [])
            },
            "createdAt": datetime.now(),
            "updatedAt": datetime.now()
        }

        result = collection.insert_one(aegis_grader_doc)
        document_id = result.inserted_id
        print(f"Created grading document with ID: {document_id}")

        return document_id

    except Exception as e:
        print(f"Error creating grading document: {str(e)}")
        return None

def update_answer_sheet_in_document(document_id, manifest_data, evaluation_result, client):
    """Update a single answer sheet evaluation in the existing document"""
    try:
        if not evaluation_result or not document_id or not client:
            return False

        print(f"Updating answer sheet in document {document_id}...")

        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'

        db = client[db_name]
        collection = db['AegisGrader']

        # evaluation_result is a tuple: (evaluation_text, answer_sheet_key)
        evaluation_text, answer_sheet_key, answerSheetOcr, rubricContent, filterAnswers = evaluation_result

        # Find the corresponding answer sheet file info
        answer_sheet_file = None
        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'answer_sheet' and file.get('key') == answer_sheet_key:
                answer_sheet_file = file
                break

        if not answer_sheet_file:
            print(f"Could not find answer sheet file info for key: {answer_sheet_key}")
            return False

        # Determine if this is an error result
        is_error = evaluation_text.startswith("Error:") or evaluation_text.startswith("ERROR")



        answer_sheet_doc = {
            "id": answer_sheet_file.get('key'),
            "studentName": answer_sheet_file.get('studentName', ''),
            "rollNumber": answer_sheet_file.get('rollNumber', ''),
            "pdfUrl": answer_sheet_file.get('key'), 
            "timestamp": answer_sheet_file.get('timestamp'),
            "evaluationResult": json.dumps(evaluation_text),
            "ocr" : answerSheetOcr,
            "filter" : filterAnswers,
            "status": "error" if is_error else "completed",
            "processedAt": datetime.now().isoformat()
        }
        rubric_doc = {
            "ocrData" : rubricContent,
        }

        # Use $set and array_filters to update the answer sheet in the existing document
        result = collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "answerSheets.$[i]": answer_sheet_doc,
                    "rubric.ocrData": rubric_doc,
                    "updatedAt": datetime.now()
                }
            },
            array_filters=[{"i.id": answer_sheet_key}]
        )

        if result.modified_count > 0:
            print(f"Successfully updated answer sheet for student {answer_sheet_file.get('studentName', 'Unknown')}")
            return True
        else:
            print(f"Failed to update answer sheet - document not found or not modified")
            return False

    except Exception as e:
        print(f"Error updating answer sheet in MongoDB: {str(e)}")
        return False

def update_processing_stats(document_id, successful_count, failed_count, client):
    """Update the processing statistics in the grading document"""
    try:
        if not document_id or not client:
            return False

        print(f"Updating processing stats for document {document_id}...")

        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'

        db = client[db_name]
        collection = db['AegisGrader']

        # Determine overall status
        total_sheets = successful_count + failed_count
        if total_sheets == 0:
            overall_status = "completed"
        elif failed_count == 0:
            overall_status = "completed"
        elif successful_count == 0:
            overall_status = "failed"
        else:
            overall_status = "partial_completion"

        # Update processing stats
        result = collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "processingStats.successfulEvaluations": successful_count,
                    "processingStats.failedEvaluations": failed_count,
                    "processingStats.overallStatus": overall_status,
                    "processingStats.completedAt": datetime.now(),
                    "updatedAt": datetime.now()
                }
            }
        )

        if result.modified_count > 0:
            print(f"Successfully updated processing stats: {successful_count} successful, {failed_count} failed")
            return True
        else:
            print(f"Failed to update processing stats - document not found or not modified")
            return False

    except Exception as e:
        print(f"Error updating processing stats in MongoDB: {str(e)}")
        return False
