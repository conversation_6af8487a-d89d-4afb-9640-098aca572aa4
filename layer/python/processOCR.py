# Standard library imports
import json
import re
import sys
from concurrent.futures import ThreadPoolExecutor

# Third-party imports
from tqdm import tqdm # type: ignore
from langchain_core.documents import Document

# Local imports
from preProcessPDF import extract_page, get_pdf_reader_and_metadata
from model import generate, evaluate
from mongoOps import get_mongo_client, save_progress
from newPrompts import ocr_processing_prompts, evaluation_prompts, rubric_generation_and_validator_prompts

subject_to_prompt_mapping = {
    "Paper A: Compulsory Indian Language": "paper_a_indian_language",
    "Paper B: Compulsory English": "paper_b_english",
    "General Studies Paper I": "gs_1",
    "General Studies Paper II": "gs_2",
    "General Studies Paper III": "gs_3",
    "General Studies Paper IV": "gs_4",
    "Essay": "essay",
    "Optional Subject": "optional_subject"
}

def reorder_pages(pages, generic_metadata):
    '''
    Return type should be list of {page_content: "content", metadata: {generic + page number}}
    '''
    pattern = r'page_number:\s*\"?(\d+)\"?'

    documentList = []

    for page in pages:
        # find the page number in the content
        match = re.search(pattern, page)
        metadata = generic_metadata.copy()
        if match:
            page_number = int(match.group(1))
            metadata['page'] = page_number
            documentList.append(Document(page_content= page, metadata=metadata))
        else:
            print(f'Warning: page number not found in the content')

    # sort the documents by metadata page number
    documentList.sort(key=lambda doc: doc.metadata.get('page', 0))
    return documentList

def process_all_pages(pdf_path, file_type, subject, jobid, mongoClient):
    """
    Processes all pages in a PDF and returns an array of Document objects.
    """
    reader, metadata = get_pdf_reader_and_metadata(pdf_path)
    documents = []

    
    for page_number in tqdm(range(len(reader.pages)), desc=f"Processing {file_type} pages"):
        page_metadata = metadata.copy()
        page_metadata['page'] = page_number + 1
        page_metadata['jobid'] = jobid
        save_progress(mongoClient, page_number/len(reader.pages), jobid)
        
        b64_page = extract_page(reader, page_number)
        if subject in subject_to_prompt_mapping:
            prompt = ocr_processing_prompts["answer_sheet_ocr"] if file_type == "answer_sheet" else ocr_processing_prompts["document_page_ocr"]
        else:
            prompt = ocr_processing_prompts["document_page_ocr"] # Default prompt to prevent crash
        document_page = generate(b64_page, prompt, page_metadata)

        documents.append(document_page)

    # update to 100 % completion last update before closing client
    save_progress(mongoClient, 1.0, jobid)


    return documents

def removeDuplicates(data):
    unique = []
    seen = set()
    for item in data:
        if item['questionNumber'] not in seen:
            seen.add(item['questionNumber'])
            unique.append(item)
    return unique

def clean_json_response(raw_content):
    """
    Clean JSON response by removing markdown code blocks if present
    """
    content = raw_content.strip()

    if content.startswith('```json'):
        # Remove ```json from start and ``` from end
        content = content[7:]  # Remove ```json
        if content.endswith('```'):
            content = content[:-3]  # Remove ```
        content = content.strip()

    elif content.startswith('```'):
        # Remove ``` from start and end (generic markdown)
        content = content[3:]
        if content.endswith('```'):
            content = content[:-3]
        content = content.strip()


    return content


def combine_and_evaluate(answer_sheet_docs, rubric_data, subject, jobId, client):
    """
    Optimized version that accepts document objects directly instead of file paths.
    answer_sheet_docs: List of Document objects from OCR
    rubric_data: String content of rubric (from file path or direct content)
    """

    # Extract content from answer sheet documents
    answer_sheet_data = ""
    if isinstance(answer_sheet_docs, list):
        # If it's a list of Document objects
        answer_sheet_data = extract_content_from_docs(answer_sheet_docs)
    else:
        # Fallback: if it's a file path (for backward compatibility)
        with open(answer_sheet_docs, 'r') as jsonl_file:
            for line in jsonl_file:
                try:
                    data = json.loads(line)
                    answer_sheet_data += data['page_content'] + "\n\n"
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSONL line in answer sheet: {str(e)}", file=sys.stderr)
                except Exception as e:
                    print(f"Some other error please check: {str(e)}", file=sys.stderr)

    print("Answer sheet data extracted successfully", file=sys.stderr)
    print("Rubric data extracted successfully", file=sys.stderr)


    evaluation_prompt = evaluation_prompts[subject_to_prompt_mapping[subject]]
    filter_prompt_answer = ocr_processing_prompts["answer_validator"]

    try:
        save_progress(client, 1.0, jobId, "evaluating")

        # Step 1: Answer validation with detailed error handling
        print(f"[DEBUG] Starting answer validation for job {jobId}", file=sys.stderr)
        try:
            validation_result = evaluate(answer_sheet_data, rubric_data, filter_prompt_answer, format='application/json')
            print(f"[DEBUG] Answer validation completed, result length: {len(validation_result.page_content)}", file=sys.stderr)

            # Clean the result - remove markdown code blocks if present
            cleaned_content = clean_json_response(validation_result.page_content)
            filterAnswerSheet = json.loads(cleaned_content)
            print(f"[DEBUG] Answer validation JSON parsed successfully", file=sys.stderr)
        except json.JSONDecodeError as e:
            print(f"[ERROR] Answer validation returned invalid JSON: {e}", file=sys.stderr)
            print(f"[ERROR] Raw validation result: {validation_result.page_content[:500]}...", file=sys.stderr)
            raise RuntimeError(f"Answer validation failed: Invalid JSON returned")
        except Exception as e:
            print(f"[ERROR] Answer validation failed: {e}", file=sys.stderr)
            raise

        # Step 2: Rubric parsing with error handling
        try:
            rubric_content = json.loads(rubric_data)
            print(f"[DEBUG] Rubric JSON parsed successfully", file=sys.stderr)
        except json.JSONDecodeError as e:
            print(f"[ERROR] Rubric data is invalid JSON: {e}", file=sys.stderr)
            print(f"[ERROR] Raw rubric data: {rubric_data[:500]}...", file=sys.stderr)
            raise RuntimeError(f"Rubric parsing failed: Invalid JSON")

        # Step 3: Data structure validation
        if 'answerData' not in filterAnswerSheet:
            print(f"[ERROR] filterAnswerSheet missing 'answerData' key. Keys: {list(filterAnswerSheet.keys())}", file=sys.stderr)
            raise RuntimeError("Answer validation result missing 'answerData'")

        if 'questionsData' not in rubric_content:
            print(f"[ERROR] rubric_content missing 'questionsData' key. Keys: {list(rubric_content.keys())}", file=sys.stderr)
            raise RuntimeError("Rubric content missing 'questionsData'")

        # Step 4: Remove duplicates with error handling
        try:
            original_answer_count = len(filterAnswerSheet['answerData'])
            original_rubric_count = len(rubric_content['questionsData'])

            filterAnswerSheet['answerData'] = removeDuplicates(filterAnswerSheet['answerData'])
            rubric_content['questionsData'] = removeDuplicates(rubric_content['questionsData'])

            final_answer_count = len(filterAnswerSheet['answerData'])
            final_rubric_count = len(rubric_content['questionsData'])

            print(f"[DEBUG] Duplicates removed - Answers: {original_answer_count} -> {final_answer_count}, Rubric: {original_rubric_count} -> {final_rubric_count}", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] Error removing duplicates: {e}", file=sys.stderr)
            raise


        # Step 5: Length validation and evaluation
        answer_count = len(filterAnswerSheet['answerData'])
        rubric_count = len(rubric_content['questionsData'])
        print(f"[DEBUG] Final counts - Answers: {answer_count}, Rubric: {rubric_count}", file=sys.stderr)

        if answer_count == 0:
            print(f"[ERROR] No answers found after validation", file=sys.stderr)
            raise RuntimeError("No valid answers found")

        if rubric_count == 0:
            print(f"[ERROR] No questions found in rubric", file=sys.stderr)
            raise RuntimeError("No questions found in rubric")

        if answer_count != rubric_count:
            print(f"[WARNING] Answer count ({answer_count}) != Rubric count ({rubric_count})", file=sys.stderr)
            print(f"[WARNING] This will skip evaluation. Proceeding with available data.", file=sys.stderr)

        # threadpool to break it in the questions and evalutate each question simultaneously
        numThreads = 10
        questionEvaluation = []

        if answer_count == rubric_count:
            print(f"[DEBUG] Starting individual question evaluation with {numThreads} threads", file=sys.stderr)
            try:
                with ThreadPoolExecutor(max_workers=numThreads) as executor:
                    def evaluate_single_question(comb):
                        try:
                            answer_obj, rubric_obj = comb
                            # Validate individual objects before serialization
                            if 'questionNumber' not in answer_obj:
                                raise ValueError(f"Answer object missing questionNumber: {answer_obj}")
                            if 'questionNumber' not in rubric_obj:
                                raise ValueError(f"Rubric object missing questionNumber: {rubric_obj}")

                            answer_json = json.dumps(answer_obj)
                            rubric_json = json.dumps(rubric_obj)

                            # Replace placeholders in the evaluation prompt
                            question_specific_prompt = evaluation_prompt.replace("{questionNumber}", answer_obj['questionNumber'])
                            # Replace total_marks with the rubric's total_marks if available, otherwise use default
                            total_marks = rubric_obj.get('total_marks', '20')
                            question_specific_prompt = question_specific_prompt.replace("{total_marks}", str(total_marks))
                            print(f"[DEBUG] Evaluating question {answer_obj['questionNumber']} with total marks {total_marks}", file=sys.stderr)

                            result = evaluate(answer_json, rubric_json, question_specific_prompt)
                            print(f"[DEBUG] Evaluated question {answer_obj['questionNumber']}", file=sys.stderr)
                            return result
                        except Exception as e:
                            print(f"[ERROR] Failed to evaluate question {answer_obj.get('questionNumber', 'unknown')}: {e}", file=sys.stderr)
                            raise

                    questionEvaluation = list(executor.map(evaluate_single_question, zip(filterAnswerSheet['answerData'], rubric_content['questionsData'])))
                    print(f"[DEBUG] Completed {len(questionEvaluation)} question evaluations", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] Error during parallel evaluation: {e}", file=sys.stderr)
                raise
        else:
            print(f"[WARNING] Skipping evaluation due to count mismatch", file=sys.stderr)



        # Extract page_content from each Document object and convert to JSON
        evaluation_results = []
        for i, doc in enumerate(questionEvaluation):
            if hasattr(doc, 'page_content'):
                try:
                    # Clean the content to remove markdown formatting
                    cleaned_content = clean_json_response(doc.page_content)
                    print(f"[DEBUG] Question {i} cleaned content length: {len(cleaned_content)}", file=sys.stderr)
                    print(f"[DEBUG] Question {i} cleaned content preview: {cleaned_content[:100]}...", file=sys.stderr)

                    # Parse the cleaned JSON and append directly to the list
                    evaluation_json = json.loads(cleaned_content)
                    evaluation_results.append(evaluation_json)
                    print(f"[DEBUG] Successfully parsed evaluation JSON for question {i}, keys: {list(evaluation_json.keys())}", file=sys.stderr)
                except json.JSONDecodeError as e:
                    print(f"[ERROR] Failed to parse evaluation JSON for question {i}: {e}", file=sys.stderr)
                    print(f"[ERROR] Raw content: {doc.page_content[:300]}...", file=sys.stderr)
                    print(f"[ERROR] Cleaned content: {cleaned_content[:300]}...", file=sys.stderr)
                    # Fallback: add error object
                    evaluation_results.append({
                        "error": f"Failed to parse evaluation for question {i}",
                        "raw_content": doc.page_content[:500]
                    })
            else:
                print(f"[WARNING] Document {i} missing page_content", file=sys.stderr)
                evaluation_results.append({
                    "error": f"Missing evaluation content for question {i}"
                })

        print(f"[DEBUG] Extracted {len(evaluation_results)} evaluation results", file=sys.stderr)
        save_progress(client, 1.0, jobId, "completed")
        return str(filterAnswerSheet), evaluation_results
    except Exception as e:
        raise RuntimeError(f"Error during evaluation: {str(e)}")
    
    
def extract_content_from_docs(docs):
    """
    Helper function to extract page_content from a list of Document objects.
    """
    content = ""
    for doc in docs:
        if hasattr(doc, 'page_content'):
            content += doc.page_content + "\n\n"
        else:
            print(f"Warning: Document object missing page_content: {doc}", file=sys.stderr)
    return content

def create_rubric(question_paper_docs, save_md_files=False):
    """
    Creates a rubric for a question paper.
    Optimized to accept Document objects directly instead of file paths.
    """

    # Extract content from documents
    if isinstance(question_paper_docs, list):
        # Direct document objects
        rubric_data = extract_content_from_docs(question_paper_docs)
    else:
        # Fallback: file path (for backward compatibility)
        rubric_data = ""
        with open(question_paper_docs, 'r') as jsonl_file:
            for line in jsonl_file:
                try:
                    data = json.loads(line)
                    rubric_data += data['page_content'] + "\n\n"
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSONL line in created rubric: {str(e)}", file=sys.stderr)

    # Optional: Save to markdown file for debugging
    # if save_md_files:
    #     with open('/tmp/created_rubric_data.md', 'w') as md_file:
    #         md_file.write(rubric_data)
    #     print("Debug: Created rubric data saved to markdown file", file=sys.stderr)

    print("Created rubric data extracted successfully", file=sys.stderr)

    return rubric_data

